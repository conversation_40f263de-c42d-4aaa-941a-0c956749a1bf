import { system } from "@minecraft/server";
import { executeSpinSlamAttack } from "./spin_slam";
import { stopPiglinChampionSounds } from "../soundManager";
/**
 * Attack timing and duration constants for spin slam attack
 */
const DAMAGE_TIMING_PHASE1 = 83; // When first damage is applied (ticks)
const DAMAGE_TIMING_PHASE2 = 124; // When second damage is applied (ticks)
const ANIMATION_TIME = 178; // Total animation duration (ticks)
const COOLDOWN_TIME = 80; // Cooldown after attack (ticks)
/**
 * Execute spin slam attack with runTimeout-based timing
 * @param piglinChampion The piglin champion entity
 */
export function executeSpinSlamAttackSequence(piglinChampion) {
    // Schedule first damage phase at the correct timing
    const damageTimeout1 = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(damageTimeout1);
                return;
            }
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (currentAttack === "spin_slam") {
                executeSpinSlamAttack(piglin<PERSON>hampion, 1); // Phase 1
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(damageTimeout1);
        }
    }, DAMAGE_TIMING_PHASE1);
    // Schedule second damage phase at the correct timing
    const damageTimeout2 = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(damageTimeout2);
                return;
            }
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (currentAttack === "spin_slam") {
                executeSpinSlamAttack(piglinChampion, 2); // Phase 2
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(damageTimeout2);
        }
    }, DAMAGE_TIMING_PHASE2);
    // Schedule attack reset when animation completes
    const resetTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTimeout);
                return;
            }
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (currentAttack === "spin_slam") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(resetTimeout);
        }
    }, ANIMATION_TIME);
    // Schedule cooldown completion
    const cooldownTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTimeout);
                return;
            }
            // Set cooling_down to false to allow next attack selection
            piglinChampion.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(cooldownTimeout);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
