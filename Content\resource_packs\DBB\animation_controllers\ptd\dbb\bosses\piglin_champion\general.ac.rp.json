{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb_piglin_champion.general": {"states": {"default": {"animations": [{"spawn": "q.property('ptd_dbb:spawning') == true"}], "transitions": [{"idling": "q.property('ptd_dbb:spawning') == false"}], "blend_transition": 0.3}, "idling": {"animations": ["idle"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"walking": "q.ground_speed > 0.3"}, {"dead": "q.property('ptd_dbb:dead') == true"}, {"horizontal_attack": "q.property('ptd_dbb:attack') == 'horizontal'"}, {"vertical_attack": "q.property('ptd_dbb:attack') == 'vertical'"}, {"foot_stomp": "q.property('ptd_dbb:attack') == 'foot_stomp'"}, {"spin_slam": "q.property('ptd_dbb:attack') == 'spin_slam'"}, {"body_slam": "q.property('ptd_dbb:attack') == 'body_slam'"}, {"upchuck": "q.property('ptd_dbb:attack') == 'upchuck'"}, {"charging": "q.property('ptd_dbb:attack') == 'charging'"}, {"healing": "q.property('ptd_dbb:attack') == 'healing'"}, {"summoning_chant": "q.property('ptd_dbb:attack') == 'summoning_chant'"}], "blend_transition": 0.3}, "walking": {"animations": ["walk"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.ground_speed < 0.3"}, {"dead": "q.property('ptd_dbb:dead') == true"}, {"horizontal_attack": "q.property('ptd_dbb:attack') == 'horizontal'"}, {"vertical_attack": "q.property('ptd_dbb:attack') == 'vertical'"}, {"foot_stomp": "q.property('ptd_dbb:attack') == 'foot_stomp'"}, {"spin_slam": "q.property('ptd_dbb:attack') == 'spin_slam'"}, {"body_slam": "q.property('ptd_dbb:attack') == 'body_slam'"}, {"upchuck": "q.property('ptd_dbb:attack') == 'upchuck'"}, {"charging": "q.property('ptd_dbb:attack') == 'charging'"}, {"healing": "q.property('ptd_dbb:attack') == 'healing'"}, {"summoning_chant": "q.property('ptd_dbb:attack') == 'summoning_chant'"}], "blend_transition": 0.3}, "horizontal_attack": {"animations": ["horizontal_attack"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "vertical_attack": {"animations": ["vertical_attack"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "foot_stomp": {"animations": ["foot_stomp"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "spin_slam": {"animations": ["spin_slam"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "body_slam": {"animations": ["body_slam"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "upchuck": {"animations": ["upchuck"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "charging": {"animations": ["charge_1"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"charging_2": "q.all_animations_finished"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "charging_2": {"animations": ["charge_2"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"charging_3": "q.all_animations_finished"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "charging_3": {"animations": ["charge_3"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"charging_stunned_sitting": "q.all_animations_finished"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.5}, "charging_stunned_sitting": {"animations": ["stunned_sitting"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"charging_4": "q.all_animations_finished"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.5}, "charging_4": {"animations": ["charge_4"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "healing": {"animations": ["healing"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"upchuck": "q.property('ptd_dbb:attack') == 'upchuck'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "summoning_chant": {"animations": ["summoning_chant"], "transitions": [{"stunned": "q.property('ptd_dbb:attack') == 'stunned_standing' || q.property('ptd_dbb:attack') == 'stunned_sitting'"}, {"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "stunned": {"animations": ["stun"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "dead": {"animations": ["death"]}}}}}