import { system } from "@minecraft/server";
import { executeChargingAttack, startContinuousChargingDamage } from "./charging";
import { stopPiglinChampionSounds } from "../soundManager";
/**
 * Attack timing and duration constants for charging attack
 */
const SPEED_TIMING = 30; // When speed effect is applied (ticks)
const CONTINUOUS_DAMAGE_TIMING = 46; // When continuous damage starts (ticks)
const FINAL_DAMAGE_TIMING = 96; // When final impact damage is applied (ticks)
const ANIMATION_TIME = 315; // Total animation duration (ticks)
const COOLDOWN_TIME = 120; // Cooldown after attack (ticks)
/**
 * Execute charging attack with runTimeout-based timing
 * @param piglinChampion The piglin champion entity
 */
export function executeChargingAttackSequence(piglinChampion) {
    // Schedule speed effect at the start of charge2 animation (tick 30)
    const speedTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(speedTimeout);
                return;
            }
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (currentAttack === "charging") {
                // Apply speed 3 effect for the duration of charge_2 and charge_3 (60 ticks)
                piglinChampion.addEffect("minecraft:speed", 60, { amplifier: 3, showParticles: false });
                piglinChampion.triggerEvent("ptd_dbb:charging2"); // Increase speed in the component groups
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(speedTimeout);
        }
    }, SPEED_TIMING);
    // Schedule continuous damage start
    const continuousDamageTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(continuousDamageTimeout);
                return;
            }
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (currentAttack === "charging") {
                startContinuousChargingDamage(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(continuousDamageTimeout);
        }
    }, CONTINUOUS_DAMAGE_TIMING);
    // Schedule final impact damage
    const finalDamageTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(finalDamageTimeout);
                return;
            }
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (currentAttack === "charging") {
                executeChargingAttack(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(finalDamageTimeout);
        }
    }, FINAL_DAMAGE_TIMING);
    // Schedule attack reset when animation completes
    const resetTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTimeout);
                return;
            }
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (currentAttack === "charging") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
                // Recover from stun after charging attack - add the melee component group back
                piglinChampion.triggerEvent("ptd_dbb:recover_after_charge");
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(resetTimeout);
        }
    }, ANIMATION_TIME);
    // Schedule cooldown completion
    const cooldownTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTimeout);
                return;
            }
            // Set cooling_down to false to allow next attack selection
            piglinChampion.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(cooldownTimeout);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
