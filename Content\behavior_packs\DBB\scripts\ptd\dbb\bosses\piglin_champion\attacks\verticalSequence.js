import { system } from "@minecraft/server";
import { executeVerticalAttack } from "./vertical";
import { stopPiglinChampionSounds } from "../soundManager";
/**
 * Attack timing and duration constants for vertical attack
 */
const DAMAGE_TIMING = 44; // When damage is applied (ticks)
const ANIMATION_TIME = 106; // Total animation duration (ticks)
const COOLDOWN_TIME = 40; // Cooldown after attack (ticks)
/**
 * Execute vertical attack with runTimeout-based timing
 * @param piglinChampion The piglin champion entity
 */
export function executeVerticalAttackSequence(piglinChampion) {
    // Schedule damage at the correct timing
    const damageTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(damageTimeout);
                return;
            }
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (currentAttack === "vertical") {
                executeVerticalAttack(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(damageTimeout);
        }
    }, DAMAGE_TIMING);
    // Schedule attack reset when animation completes
    const resetTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTimeout);
                return;
            }
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (currentAttack === "vertical") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(resetTimeout);
        }
    }, ANIMATION_TIME);
    // Schedule cooldown completion
    const cooldownTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTimeout);
                return;
            }
            // Set cooling_down to false to allow next attack selection
            piglinChampion.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(cooldownTimeout);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
