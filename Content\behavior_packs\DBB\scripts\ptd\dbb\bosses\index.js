import { piglinChampionMechanics } from "./piglin_champion/index";
import { necromancerMechanics } from "./necromancer/index";
import { grimhowlMechanics } from "./grimhowl/index";
export const bossIds = new Set([
    "ptd_dbb:piglin_champion",
    "ptd_dbb:necromancer",
    "ptd_dbb:wardzilla",
    "ptd_dbb:void_hydra",
    "ptd_dbb:grimhowl"
]);
export function handleBossMechanics(entity, bossTypeid) {
    // Handle piglin champion mechanics
    if (bossTypeid === "ptd_dbb:piglin_champion") {
        piglinChampionMechanics(entity);
    }
    // Handle necromancer mechanics
    if (bossTypeid === "ptd_dbb:necromancer") {
        necromancerMechanics(entity);
    }
    // Handle Grimhowl (death) mechanics
    if (bossTypeid === "ptd_dbb:grimhowl") {
        grimhowlMechanics(entity, "death");
    }
}
