import { Entity } from "@minecraft/server";
import { handleDeathMechanics } from "../bosses/general_mechanics/deathMechanics";
import { spawnItemFountain } from "../bosses/general_mechanics/itemFountain";
import { stopPiglinChampionSounds } from "../bosses/piglin_champion/soundManager";

export function handleLootMechanics(entity: Entity, currentTick: number) {
  const entityTypeId = entity.typeId;
  switch (entityTypeId) {
    case "ptd_dbb:winged_zombie":
      if (
        handleDeathMechanics(entity, {
          // Configure death mechanics specific to the Winged Zombie
          duration: 36,
          xpOrbs: {
            count: 2,
            duration: 10,
            heightOffset: 1
          },
          // No drops here as we'll use a custom event to spawn the essence fountain
          drops: [],
          deathSound: "mob.zombie.death",
          // Basic stopping function, as zombie minions don't have special sounds
          stopSoundsFn: () => {
            /* No special sounds to stop */
          }
        }, currentTick)
      ) {
        try {
          // Create a small fountain of rotten flesh if this is tick 1 of death
          if (currentTick === 1) {
            // Create a fountain of 2 rotten flesh
            spawnItemFountain(entity, "minecraft:rotten_flesh", 2, {
              heightOffset: 1,
              soundEffect: "random.pop"
            });
          }
        } catch (e) {}
      }
      break;
    case "ptd_dbb:zombie_brute":
      if (
        handleDeathMechanics(entity, {
          // Configure death mechanics specific to the Zombie Brute
          duration: 36,
          xpOrbs: {
            count: 3,
            duration: 10,
            heightOffset: 1
          },
          // No drops here as we'll use a custom event to spawn the essence fountain
          drops: [],
          deathSound: "mob.zombie.death",
          // Basic stopping function, as zombie minions don't have special sounds
          stopSoundsFn: () => {
            /* No special sounds to stop */
          }
        }, currentTick)
      ) {
        try {
          // Create a larger fountain of rotten flesh if this is tick 1 of death
          if (currentTick === 1) {
            // Create a fountain of 3-4 rotten flesh (using two fountains for randomness)
            spawnItemFountain(entity, "minecraft:rotten_flesh", 3, {
              heightOffset: 1,
              soundEffect: "random.pop"
            });

            // 50% chance for an extra piece of rotten flesh
            if (Math.random() < 0.5) {
              spawnItemFountain(entity, "minecraft:rotten_flesh", 1, {
                heightOffset: 1
              });
            }
          }
        } catch (e) {}
      }
      break;
    case "ptd_dbb:skeleton_soul":
      if (
        handleDeathMechanics(entity, {
          // Configure death mechanics specific to the Skeleton Soul
          duration: 20,
          xpOrbs: {
            count: 2,
            duration: 10,
            heightOffset: 1
          },
          // No drops here as we'll use a custom event to spawn the essence fountain
          drops: [],
          deathSound: "mob.wither_skeleton.death",
          // Basic stopping function, as skeleton minions don't have special sounds
          stopSoundsFn: () => {
            /* No special sounds to stop */
          }
        }, currentTick)
      ) {
        try {
          // Create a small fountain of bones if this is tick 1 of death
          if (currentTick === 1) {
            // Create a fountain of 1-2 bones
            spawnItemFountain(entity, "minecraft:bone", 1, {
              heightOffset: 1,
              soundEffect: "random.pop"
            });

            // 70% chance for an extra bone
            if (Math.random() < 0.7) {
              spawnItemFountain(entity, "minecraft:bone", 1, {
                heightOffset: 1
              });
            }

            // 15% chance for a small soul item (if available, otherwise coal)
            if (Math.random() < 0.15) {
              spawnItemFountain(entity, "minecraft:coal", 1, {
                heightOffset: 1.2,
                soundEffect: "random.orb"
              });
            }
          }
        } catch (e) {}
      }
      break;

    case "ptd_dbb:piglin_champion":
      if (
        handleDeathMechanics(entity, {
          // Configure death mechanics specific to the Piglin Champion
          duration: 100,
          xpOrbs: {
            count: 8,
            duration: 30,
            heightOffset: 2.25,
          },
          // No drops here as we'll use a custom event to spawn the essence fountain
          drops: [],
          deathSound: "mob.ptd_dbb_piglin_champion.death",
          // Add custom event to spawn essence fountain at the beginning of death sequence
          customEvents: [
            {
              tick: 1,
              callback: (entity) => {
                entity.dimension.spawnParticle("ptd_dbb:pg_die1_01", entity.location);
                // Spawn 32 essence items in a fountain-like effect
                spawnItemFountain(entity, "ptd_dbb:piglin_champion_essence", 32, {
                  heightOffset: 2.25,
                  particleEffect: "minecraft:large_explosion",
                  soundEffect: "random.pop",
                  minVerticalStrength: 0.1,
                  maxVerticalStrength: 0.3,
                  minHorizontalStrength: 0.05,
                  maxHorizontalStrength: 0.2,
                });
              },
            },
          ],
          // Provide the sound stopping function
          stopSoundsFn: stopPiglinChampionSounds,
        }, currentTick)
      ) {
        // Death mechanics were applied successfully
      }
      break;
    case "ptd_dbb:piglin_brute":
      if (
        handleDeathMechanics(entity, {
          // Configure death mechanics specific to the Piglin Brute
          duration: 40,
          xpOrbs: {
            count: 3,
            duration: 10,
            heightOffset: 1
          },
          // No drops here as we'll use a custom event to spawn the essence fountain
          drops: [],
          deathSound: "mob.piglin.death",
          // Basic stopping function, as piglin minions don't have special sounds
          stopSoundsFn: () => {
            /* No special sounds to stop */
          }
        }, currentTick)
      ) {
        try {
          // Create a fountain of gold nuggets if this is tick 1 of death
          if (currentTick === 1) {
            // Create a fountain of 2-3 gold nuggets
            spawnItemFountain(entity, "minecraft:gold_nugget", 2, {
              heightOffset: 1,
              soundEffect: "random.pop"
            });

            // 60% chance for an extra gold nugget
            if (Math.random() < 0.6) {
              spawnItemFountain(entity, "minecraft:gold_nugget", 1, {
                heightOffset: 1
              });
            }

            // 20% chance for a gold ingot
            if (Math.random() < 0.2) {
              spawnItemFountain(entity, "minecraft:gold_ingot", 1, {
                heightOffset: 1.2,
                soundEffect: "random.orb"
              });
            }
          }
        } catch (e) {}
      }
      break;
    case "ptd_dbb:piglin_marauder":
      if (
        handleDeathMechanics(entity, {
          // Configure death mechanics specific to the Piglin Marauder
          duration: 52, // 1.0417 seconds * 50 = ~52 ticks
          xpOrbs: {
            count: 5,
            duration: 15,
            heightOffset: 1.5
          },
          // No drops here as we'll use a custom event to spawn the essence fountain
          drops: [],
          deathSound: "mob.piglin.death",
          // Basic stopping function, as piglin minions don't have special sounds
          stopSoundsFn: () => {
            /* No special sounds to stop */
          }
        }, currentTick)
      ) {
        try {
          // Create a fountain of gold items if this is tick 1 of death
          if (currentTick === 1) {
            // Create a fountain of 3-4 gold nuggets
            spawnItemFountain(entity, "minecraft:gold_nugget", 3, {
              heightOffset: 1.5,
              soundEffect: "random.pop"
            });

            // 80% chance for an extra gold nugget
            if (Math.random() < 0.8) {
              spawnItemFountain(entity, "minecraft:gold_nugget", 1, {
                heightOffset: 1.5
              });
            }

            // 40% chance for a gold ingot
            if (Math.random() < 0.4) {
              spawnItemFountain(entity, "minecraft:gold_ingot", 1, {
                heightOffset: 1.7,
                soundEffect: "random.orb"
              });
            }

            // 15% chance for an additional gold ingot (rare drop)
            if (Math.random() < 0.15) {
              spawnItemFountain(entity, "minecraft:gold_ingot", 1, {
                heightOffset: 1.9,
                soundEffect: "random.levelup"
              });
            }
          }
        } catch (e) {}
      }
      break;
    default:
      break;
  }
}
